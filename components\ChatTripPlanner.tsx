import React, { useCallback } from 'react';
import { Route, TripResult } from '../types';
import { Location } from '../App';
import { useChat } from '../hooks/useChat';
import { createChatPrompt, createSimpleResponsePrompt } from '../utils/chatPrompts';
import ChatHistory from './ChatHistory';
import ChatInput from './ChatInput';
import QuickActions from './QuickActions';
import ErrorDisplay from './ErrorDisplay';

interface ChatTripPlannerProps {
    allRoutes: Route[];
    locations: Location[];
    apiKey: string;
}

const ChatTripPlanner: React.FC<ChatTripPlannerProps> = ({ allRoutes, locations, apiKey }) => {
    const {
        messages,
        isLoading,
        error,
        addUserMessage,
        addAIMessage,
        setLoading,
        setError,
        clearHistory,
        resetError,
    } = useChat();

    const isButtonDisabled = !apiKey || isLoading;

    // Function to detect if message is a trip planning request
    const isTripPlanningRequest = (message: string): boolean => {
        const tripKeywords = [
            'from', 'to', 'get to', 'go to', 'travel', 'trip', 'route', 'how to',
            'cheapest', 'fastest', 'plan', 'journey', 'transport', 'bus', 'mtr',
            '去', '到', '點去', '搭', '坐', '路線', '交通'
        ];
        
        const lowerMessage = message.toLowerCase();
        return tripKeywords.some(keyword => lowerMessage.includes(keyword));
    };

    const handleSendMessage = useCallback(async (message: string) => {
        if (!apiKey) {
            setError("Please set your Gemini API key in the Settings tab to use the planner.");
            return;
        }

        // Add user message
        addUserMessage(message);
        setLoading(true);
        setError(null);

        try {
            let prompt: string;
            let expectTripPlan = false;

            // Determine if this is a trip planning request
            if (isTripPlanningRequest(message)) {
                prompt = createChatPrompt(message, allRoutes, locations);
                expectTripPlan = true;
            } else {
                prompt = createSimpleResponsePrompt(message);
            }

            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                tools: expectTripPlan ? [{
                    googleSearch: {}
                }] : undefined,
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: expectTripPlan ? 3072 : 1024,
                }
            };

            console.log('Making request to AI proxy...');
            const response = await fetch(`https://ai-proxy.chatwise.app/generativelanguage/v1beta/models/gemini-2.5-flash:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('AI API Error:', response.status, errorText);
                throw new Error(`AI API request failed: ${response.status} ${response.statusText}`);
            }

            const responseData = await response.json();
            console.log('AI Response:', responseData);

            if (!responseData.candidates || !responseData.candidates[0] || !responseData.candidates[0].content) {
                throw new Error('Invalid response format from AI API');
            }

            const textResponse = responseData.candidates[0].content.parts[0].text;

            if (expectTripPlan) {
                // Try to parse as trip plan JSON
                let parsedPlan: TripResult;
                
                try {
                    parsedPlan = JSON.parse(textResponse.trim());
                } catch (e) {
                    // Fallback: try to extract JSON from markdown block
                    const jsonMatch = textResponse.match(/```json\n([\s\S]*?)\n```/);
                    
                    if (jsonMatch && jsonMatch[1]) {
                        try {
                            parsedPlan = JSON.parse(jsonMatch[1]);
                        } catch (e2) {
                            console.error("Failed to parse JSON from markdown block", e2);
                            throw new Error("I couldn't generate a proper trip plan. Could you please rephrase your request?");
                        }
                    } else {
                        console.error("Failed to parse response as JSON", e);
                        console.log("Raw response:", textResponse);
                        throw new Error("I couldn't understand the trip planning format. Please try asking again.");
                    }
                }
                
                if (!parsedPlan || !parsedPlan.cheapest_plan || !parsedPlan.fastest_plan) {
                    throw new Error("The trip plan is incomplete. Please try asking again with more specific locations.");
                }
                
                // Add AI message with trip plan
                addAIMessage("Here are your trip options:", parsedPlan);
            } else {
                // Add simple text response
                addAIMessage(textResponse.trim());
            }

        } catch (err) {
            console.error("Error generating response:", err);
            const errorMessage = (err instanceof Error) ? err.message : String(err);
            
            if (errorMessage.toLowerCase().includes('api key not valid')) {
                setError("Your API key appears to be invalid. Please check it in the Settings tab.");
            } else if (errorMessage.includes('API key is missing')) {
                setError("Please set your Gemini API key in the Settings tab to use the planner.");
            } else {
                addAIMessage(`Sorry, I encountered an error: ${errorMessage}`);
            }
        } finally {
            setLoading(false);
        }
    }, [apiKey, allRoutes, locations, addUserMessage, addAIMessage, setLoading, setError]);

    return (
        <div className="flex flex-col h-full" role="main" aria-label="AI Trip Planner Chat Interface">
            {/* Header */}
            <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-3 sm:p-4">
                <h1 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white text-center">
                    AI Trip Planner Chat
                </h1>
                <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 text-center mt-1">
                    Ask me about your Hong Kong journey
                </p>
            </header>

            {/* API Key Warning */}
            {!apiKey && (
                <div
                    className="bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800 p-3 sm:p-4"
                    role="alert"
                    aria-live="polite"
                >
                    <div className="flex items-start sm:items-center">
                        <svg
                            className="h-5 w-5 text-yellow-400 mr-2 mt-0.5 sm:mt-0 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                        >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <span className="text-xs sm:text-sm text-yellow-800 dark:text-yellow-200">
                            Please go to the <strong>Settings</strong> tab to add your Gemini API key.
                        </span>
                    </div>
                </div>
            )}

            {/* Error Display */}
            {error && (
                <div className="p-3 sm:p-4" role="alert" aria-live="assertive">
                    <ErrorDisplay message={error} />
                </div>
            )}

            {/* Chat History */}
            <ChatHistory
                messages={messages}
                isLoading={isLoading}
                onClearHistory={clearHistory}
            />

            {/* Quick Actions - Show when no messages and not loading */}
            {messages.length === 0 && !isLoading && apiKey && (
                <QuickActions
                    onActionClick={handleSendMessage}
                    disabled={isButtonDisabled}
                />
            )}

            {/* Chat Input */}
            <ChatInput
                onSendMessage={handleSendMessage}
                disabled={isButtonDisabled}
                placeholder={apiKey ? "Ask me about your trip in Hong Kong..." : "Please set your API key first..."}
            />
        </div>
    );
};

export default ChatTripPlanner;

import React, { useCallback } from 'react';
import { Route, TripResult } from '../types';
import { Location } from '../App';
import { useChat } from '../hooks/useChat';
import { createChatPrompt, createSimpleResponsePrompt } from '../utils/chatPrompts';
import ChatHistory from './ChatHistory';
import ChatInput from './ChatInput';
import QuickActions from './QuickActions';
import ErrorDisplay from './ErrorDisplay';

interface ChatTripPlannerProps {
    allRoutes: Route[];
    locations: Location[];
    apiKey: string;
}

const ChatTripPlanner: React.FC<ChatTripPlannerProps> = ({ allRoutes, locations, apiKey }) => {
    const {
        messages,
        isLoading,
        error,
        addUserMessage,
        addAIMessage,
        setLoading,
        setError,
        clearHistory,
        resetError,
    } = useChat();

    const isButtonDisabled = !apiKey || isLoading;

    // Function to detect if message is a trip planning request
    const isTripPlanningRequest = (message: string): boolean => {
        const tripKeywords = [
            'from', 'to', 'get to', 'go to', 'travel', 'trip', 'route', 'how to',
            'cheapest', 'fastest', 'plan', 'journey', 'transport', 'bus', 'mtr',
            '去', '到', '點去', '搭', '坐', '路線', '交通'
        ];
        
        const lowerMessage = message.toLowerCase();
        return tripKeywords.some(keyword => lowerMessage.includes(keyword));
    };

    const handleSendMessage = useCallback(async (message: string) => {
        if (!apiKey) {
            setError("Please set your Gemini API key in the Settings tab to use the planner.");
            return;
        }

        // Add user message
        addUserMessage(message);
        setLoading(true);
        setError(null);

        try {
            let prompt: string;
            let expectTripPlan = false;

            // Determine if this is a trip planning request
            if (isTripPlanningRequest(message)) {
                prompt = createChatPrompt(message, allRoutes, locations);
                expectTripPlan = true;
            } else {
                prompt = createSimpleResponsePrompt(message);
            }

            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                tools: expectTripPlan ? [{
                    googleSearch: {}
                }] : undefined,
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: expectTripPlan ? 3072 : 1024,
                }
            };

            console.log('Making request to AI proxy...');
            const response = await fetch(`https://ai-proxy.chatwise.app/generativelanguage/v1beta/models/gemini-2.5-flash:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('AI API Error:', response.status, errorText);
                throw new Error(`AI API request failed: ${response.status} ${response.statusText}`);
            }

            const responseData = await response.json();
            console.log('AI Response:', responseData);

            if (!responseData.candidates || !responseData.candidates[0] || !responseData.candidates[0].content) {
                throw new Error('Invalid response format from AI API');
            }

            const textResponse = responseData.candidates[0].content.parts[0].text;

            if (expectTripPlan) {
                // Try to parse as trip plan JSON
                let parsedPlan: TripResult;
                
                try {
                    parsedPlan = JSON.parse(textResponse.trim());
                } catch (e) {
                    // Fallback: try to extract JSON from markdown block
                    const jsonMatch = textResponse.match(/```json\n([\s\S]*?)\n```/);
                    
                    if (jsonMatch && jsonMatch[1]) {
                        try {
                            parsedPlan = JSON.parse(jsonMatch[1]);
                        } catch (e2) {
                            console.error("Failed to parse JSON from markdown block", e2);
                            throw new Error("I couldn't generate a proper trip plan. Could you please rephrase your request?");
                        }
                    } else {
                        console.error("Failed to parse response as JSON", e);
                        console.log("Raw response:", textResponse);
                        throw new Error("I couldn't understand the trip planning format. Please try asking again.");
                    }
                }
                
                if (!parsedPlan || !parsedPlan.cheapest_plan || !parsedPlan.fastest_plan) {
                    throw new Error("The trip plan is incomplete. Please try asking again with more specific locations.");
                }
                
                // Add AI message with trip plan
                addAIMessage("Here are your trip options:", parsedPlan);
            } else {
                // Add simple text response
                addAIMessage(textResponse.trim());
            }

        } catch (err) {
            console.error("Error generating response:", err);
            const errorMessage = (err instanceof Error) ? err.message : String(err);
            
            if (errorMessage.toLowerCase().includes('api key not valid')) {
                setError("Your API key appears to be invalid. Please check it in the Settings tab.");
            } else if (errorMessage.includes('API key is missing')) {
                setError("Please set your Gemini API key in the Settings tab to use the planner.");
            } else {
                addAIMessage(`Sorry, I encountered an error: ${errorMessage}`);
            }
        } finally {
            setLoading(false);
        }
    }, [apiKey, allRoutes, locations, addUserMessage, addAIMessage, setLoading, setError]);

    return (
        <div className="flex flex-col h-full bg-gray-50 dark:bg-gray-900">
            {/* Simplified Header */}
            <div className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
                <div className="flex items-center justify-between">
                    <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Trip Planner
                    </h1>
                    {messages.length > 0 && (
                        <button
                            onClick={clearHistory}
                            className="text-sm text-gray-500 hover:text-red-600 transition-colors"
                        >
                            Clear
                        </button>
                    )}
                </div>
                {!apiKey && (
                    <div className="mt-2 text-sm text-amber-600 dark:text-amber-400">
                        ⚠️ Please set your API key in Settings
                    </div>
                )}
            </div>

            {/* Chat Messages Area */}
            <div className="flex-1 overflow-hidden flex flex-col">
                {messages.length === 0 && !isLoading ? (
                    <div className="flex-1 flex items-center justify-center p-6">
                        <div className="text-center max-w-sm">
                            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.436L3 21l2.436-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                                </svg>
                            </div>
                            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                Plan Your Hong Kong Trip
                            </h2>
                            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                                Ask me about routes, fares, or travel times
                            </p>
                            <div className="space-y-2">
                                <button
                                    onClick={() => handleSendMessage("How do I get from Tsim Sha Tsui to Central?")}
                                    disabled={isButtonDisabled}
                                    className="w-full text-left p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-colors disabled:opacity-50"
                                >
                                    <div className="text-sm font-medium text-gray-900 dark:text-white">Tsim Sha Tsui → Central</div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400">Popular route</div>
                                </button>
                                <button
                                    onClick={() => handleSendMessage("What's the cheapest way to get to the Airport?")}
                                    disabled={isButtonDisabled}
                                    className="w-full text-left p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-colors disabled:opacity-50"
                                >
                                    <div className="text-sm font-medium text-gray-900 dark:text-white">Cheapest to Airport</div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400">Budget option</div>
                                </button>
                            </div>
                        </div>
                    </div>
                ) : (
                    <ChatHistory
                        messages={messages}
                        isLoading={isLoading}
                        onClearHistory={clearHistory}
                    />
                )}
            </div>

            {/* Fixed Input Area */}
            <div className="flex-shrink-0">
                <ChatInput
                    onSendMessage={handleSendMessage}
                    disabled={isButtonDisabled}
                    placeholder={apiKey ? "Ask about your Hong Kong trip..." : "Set API key in Settings first"}
                />
            </div>
        </div>
    );
};

export default ChatTripPlanner;

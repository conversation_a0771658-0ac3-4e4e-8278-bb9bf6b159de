import React from 'react';
import { ChatMessage as ChatMessageType } from '../types';
import TripPlanResult from './TripPlanResult';

interface ChatMessageProps {
  message: ChatMessageType;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.type === 'user';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-[80%] ${isUser ? 'order-2' : 'order-1'}`}>
        {/* Avatar */}
        <div className={`flex items-center mb-2 ${isUser ? 'justify-end' : 'justify-start'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            isUser 
              ? 'bg-[color:var(--accent)] text-white' 
              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
          }`}>
            {isUser ? 'U' : 'AI'}
          </div>
          <span className={`text-xs text-gray-500 dark:text-gray-400 ${isUser ? 'mr-2' : 'ml-2'}`}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </span>
        </div>
        
        {/* Message Content */}
        <div className={`rounded-2xl px-4 py-3 ${
          isUser 
            ? 'bg-[color:var(--accent)] text-white' 
            : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700'
        }`}>
          <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
        </div>
        
        {/* Trip Plan Result (for AI messages) */}
        {!isUser && message.tripPlan && (
          <div className="mt-3">
            <TripPlanResult plan={message.tripPlan} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;

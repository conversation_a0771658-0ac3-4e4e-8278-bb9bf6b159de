import React from 'react';
import { ChatMessage as ChatMessageType } from '../types';
import TripPlanResult from './TripPlanResult';

interface ChatMessageProps {
  message: ChatMessageType;
  isLatest?: boolean;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, isLatest = false }) => {
  const isUser = message.type === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-[80%] ${isUser ? 'order-2' : 'order-1'}`}>
        {/* Message Content */}
        <div className={`rounded-lg px-4 py-3 ${
          isUser
            ? 'bg-blue-500 text-white'
            : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700'
        }`}>
          <p className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </p>
        </div>

        {/* Timestamp */}
        <div className={`mt-1 text-xs text-gray-500 dark:text-gray-400 ${
          isUser ? 'text-right' : 'text-left'
        }`}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>

        {/* Trip Plan Result (for AI messages) */}
        {!isUser && message.tripPlan && (
          <div className="mt-3">
            <TripPlanResult plan={message.tripPlan} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;

import React from 'react';

interface TypingIndicatorProps {
  message?: string;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ message = "AI is thinking..." }) => {
  return (
    <div className="flex justify-start mb-4">
      <div className="max-w-[80%]">
        {/* Typing Animation */}
        <div className="rounded-lg px-4 py-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-typing" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-typing" style={{ animationDelay: '200ms' }}></div>
            <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-typing" style={{ animationDelay: '400ms' }}></div>
          </div>
        </div>

        {/* Status */}
        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {message}
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;

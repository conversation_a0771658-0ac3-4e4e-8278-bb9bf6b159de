import { useState, useCallback, useEffect } from 'react';
import { ChatMessage, ChatConversation, ChatState, TripResult } from '../types';

const STORAGE_KEY = 'hk-transit-chat-history';

const createNewConversation = (): ChatConversation => ({
  id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  messages: [],
  createdAt: new Date(),
  lastUpdated: new Date(),
});

const createMessage = (type: 'user' | 'ai', content: string, tripPlan?: TripResult): ChatMessage => ({
  id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  type,
  content,
  timestamp: new Date(),
  tripPlan,
});

export const useChat = () => {
  const [chatState, setChatState] = useState<ChatState>(() => {
    // Try to load from localStorage
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        // Convert date strings back to Date objects
        const conversation: ChatConversation = {
          ...parsed,
          createdAt: new Date(parsed.createdAt),
          lastUpdated: new Date(parsed.lastUpdated),
          messages: parsed.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        };
        return {
          currentConversation: conversation,
          isLoading: false,
          error: null,
        };
      }
    } catch (error) {
      console.error('Failed to load chat history:', error);
    }
    
    return {
      currentConversation: createNewConversation(),
      isLoading: false,
      error: null,
    };
  });

  // Save to localStorage whenever conversation changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(chatState.currentConversation));
    } catch (error) {
      console.error('Failed to save chat history:', error);
    }
  }, [chatState.currentConversation]);

  const addUserMessage = useCallback((content: string) => {
    const message = createMessage('user', content);
    setChatState(prev => ({
      ...prev,
      currentConversation: {
        ...prev.currentConversation,
        messages: [...prev.currentConversation.messages, message],
        lastUpdated: new Date(),
      },
    }));
    return message;
  }, []);

  const addAIMessage = useCallback((content: string, tripPlan?: TripResult) => {
    const message = createMessage('ai', content, tripPlan);
    setChatState(prev => ({
      ...prev,
      currentConversation: {
        ...prev.currentConversation,
        messages: [...prev.currentConversation.messages, message],
        lastUpdated: new Date(),
      },
    }));
    return message;
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setChatState(prev => ({
      ...prev,
      isLoading: loading,
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setChatState(prev => ({
      ...prev,
      error,
    }));
  }, []);

  const clearHistory = useCallback(() => {
    const newConversation = createNewConversation();
    setChatState({
      currentConversation: newConversation,
      isLoading: false,
      error: null,
    });
    
    // Clear from localStorage
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear chat history:', error);
    }
  }, []);

  const resetError = useCallback(() => {
    setChatState(prev => ({
      ...prev,
      error: null,
    }));
  }, []);

  return {
    messages: chatState.currentConversation.messages,
    isLoading: chatState.isLoading,
    error: chatState.error,
    addUserMessage,
    addAIMessage,
    setLoading,
    setError,
    clearHistory,
    resetError,
  };
};

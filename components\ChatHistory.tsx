import React, { useEffect, useRef, useState } from 'react';
import { ChatMessage as ChatMessageType } from '../types';
import ChatMessage from './ChatMessage';
import TypingIndicator from './TypingIndicator';
import DateSeparator from './DateSeparator';
import { formatMessageDate, shouldShowDateSeparator } from '../utils/dateUtils';

interface ChatHistoryProps {
  messages: ChatMessageType[];
  isLoading?: boolean;
  onClearHistory: () => void;
}

const ChatHistory: React.FC<ChatHistoryProps> = ({ messages, isLoading = false, onClearHistory }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);

  if (messages.length === 0 && !isLoading) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6 shadow-lg">
          <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.436L3 21l2.436-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
          </svg>
        </div>
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
          Welcome to HK Transit Chat! 🚇
        </h3>
        <p className="text-gray-600 dark:text-gray-300 max-w-md mb-6 leading-relaxed">
          I'm your Hong Kong transportation assistant. Ask me about routes, fares, travel times, or any transit questions!
        </p>

        {/* Quick suggestion buttons */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">🗺️ Trip Planning</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">"How do I get from Tsim Sha Tsui to Central?"</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">💰 Best Prices</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">"What's the cheapest way to Causeway Bay?"</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">⚡ Fastest Routes</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">"Quickest way to the Airport?"</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">ℹ️ Transit Info</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">"MTR service status today"</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4" style={{ paddingBottom: '100px' }}>
      {messages.map((message, index) => {
        const previousMessage = index > 0 ? messages[index - 1] : undefined;
        const showDateSeparator = shouldShowDateSeparator(
          message.timestamp,
          previousMessage?.timestamp
        );

        return (
          <React.Fragment key={message.id}>
            {showDateSeparator && (
              <DateSeparator date={formatMessageDate(message.timestamp)} />
            )}
            <ChatMessage
              message={message}
              isLatest={index === messages.length - 1}
            />
          </React.Fragment>
        );
      })}

      {/* Typing indicator */}
      {isLoading && (
        <TypingIndicator message="Planning your route..." />
      )}

      {/* Extra space for input area */}
      <div ref={messagesEndRef} className="h-4" />
    </div>
  );
};

export default ChatHistory;

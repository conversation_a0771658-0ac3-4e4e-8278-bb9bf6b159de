import React from 'react';

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  message: string;
  category: 'planning' | 'info' | 'status';
}

interface QuickActionsProps {
  onActionClick: (message: string) => void;
  disabled?: boolean;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onActionClick, disabled = false }) => {
  const quickActions: QuickAction[] = [
    {
      id: 'tsim-central',
      label: 'Tsim Sha Tsui → Central',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
        </svg>
      ),
      message: 'How do I get from Tsim Sha Tsui to Central?',
      category: 'planning'
    },
    {
      id: 'airport-route',
      label: 'Airport Express',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
        </svg>
      ),
      message: 'What\'s the best way to get to Hong Kong International Airport?',
      category: 'planning'
    },
    {
      id: 'cheapest-route',
      label: 'Cheapest Option',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      message: 'What\'s the cheapest way to travel around Hong Kong?',
      category: 'planning'
    },
    {
      id: 'mtr-status',
      label: 'MTR Status',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      message: 'What\'s the current MTR service status?',
      category: 'status'
    },
    {
      id: 'octopus-info',
      label: 'Octopus Card',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
      ),
      message: 'Tell me about Octopus Card benefits and how to use it',
      category: 'info'
    },
    {
      id: 'night-transport',
      label: 'Night Transport',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      ),
      message: 'What are my transport options late at night in Hong Kong?',
      category: 'info'
    }
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'planning':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30';
      case 'status':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-900/30';
      case 'info':
        return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800 text-purple-700 dark:text-purple-300 hover:bg-purple-100 dark:hover:bg-purple-900/30';
      default:
        return 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700';
    }
  };

  return (
    <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        Quick Actions
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
        {quickActions.map((action) => (
          <button
            key={action.id}
            onClick={() => !disabled && onActionClick(action.message)}
            disabled={disabled}
            className={`p-3 rounded-lg border text-left transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 ${getCategoryColor(action.category)}`}
          >
            <div className="flex items-center space-x-2 mb-1">
              {action.icon}
              <span className="text-xs font-medium truncate">{action.label}</span>
            </div>
          </button>
        ))}
      </div>
      
      <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 text-center">
        Click any button to start a conversation
      </div>
    </div>
  );
};

export default QuickActions;

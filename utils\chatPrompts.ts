import { Route, Location } from '../types';
import { mtrLines, mtrStations } from '../data/mtrStations';

const stripParentheses = (text: string): string => {
  return text.replace(/\s*\([^)]*\)/g, '');
};

export const createChatPrompt = (
  userMessage: string,
  allRoutes: Route[],
  locations: Location[]
): string => {
  const kmbRouteData = allRoutes
    .filter(r => r.bound === 'O')
    .map(r => `${r.route}: ${stripParentheses(r.orig_tc)} to ${stripParentheses(r.dest_tc)}`)
    .join('\n');
    
  const mtrLineData = mtrLines.map(line => {
    const stations = mtrStations[line.code].map(s => stripParentheses(s.name_tc)).join(', ');
    return `${line.name} (${line.code}): ${stations}`;
  }).join('\n');
  
  const validLocationsData = locations.map(l => stripParentheses(l.name_tc)).join('\n');

  return `You are a Hong Kong transportation expert. Help plan trips with the cheapest and fastest options.

USER REQUEST: "${userMessage}"

TASK: Generate two trip plans (cheapest and fastest) based on the user's request.

RULES:
1. Use Google Search for current fares and travel times
2. All locations must match the provided transport hubs exactly
3. Return ONLY valid JSON (no markdown, no explanations)
4. Include real-time service conditions if relevant

JSON FORMAT:
{
  "cheapest_plan": {
    "current_conditions": "Service status (optional)",
    "total_time_minutes": number,
    "total_cost_hkd": number,
    "plan": [
      {
        "type": "walk|bus|mtr",
        "summary": "Step description in Traditional Chinese",
        "details": {
          // For walk: {"instruction": "directions"}
          // For bus: {"route": "1A", "boarding_stop": "stop", "alighting_stop": "stop", "num_stops": 5}
          // For mtr: {"line": "荃灣綫", "boarding_station": "station", "alighting_station": "station", "direction": "往荃灣", "num_stops": 2}
        },
        "duration_minutes": number,
        "cost_hkd": number
      }
    ]
  },
  "fastest_plan": {
    // Same structure as cheapest_plan
  }
}

AVAILABLE ROUTES:
${kmbRouteData}

MTR LINES:
${mtrLineData}

TRANSPORT HUBS:
${validLocationsData}`;
};

export const createSimpleResponsePrompt = (userMessage: string): string => {
  return `You are a helpful Hong Kong transportation assistant. The user asked: "${userMessage}"

Respond naturally and helpfully. If this is a trip planning request, let them know you'll help plan their route. If it's a general question about Hong Kong transport, answer it directly.

Keep your response concise and friendly.`;
};

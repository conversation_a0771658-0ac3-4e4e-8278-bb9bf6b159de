export const formatMessageDate = (date: Date): string => {
  const now = new Date();
  const messageDate = new Date(date);
  
  // Check if it's today
  if (messageDate.toDateString() === now.toDateString()) {
    return 'Today';
  }
  
  // Check if it's yesterday
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (messageDate.toDateString() === yesterday.toDateString()) {
    return 'Yesterday';
  }
  
  // Check if it's within the last week
  const weekAgo = new Date(now);
  weekAgo.setDate(weekAgo.getDate() - 7);
  if (messageDate > weekAgo) {
    return messageDate.toLocaleDateString('en-US', { weekday: 'long' });
  }
  
  // For older messages
  return messageDate.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: messageDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
  });
};

export const shouldShowDateSeparator = (currentMessage: Date, previousMessage?: Date): boolean => {
  if (!previousMessage) return true;
  
  const current = new Date(currentMessage);
  const previous = new Date(previousMessage);
  
  // Show separator if messages are on different days
  return current.toDateString() !== previous.toDateString();
};

export const formatTime = (date: Date): string => {
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

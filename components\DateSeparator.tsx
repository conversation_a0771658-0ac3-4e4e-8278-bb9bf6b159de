import React from 'react';

interface DateSeparatorProps {
  date: string;
}

const DateSeparator: React.FC<DateSeparatorProps> = ({ date }) => {
  return (
    <div className="flex items-center justify-center my-6">
      <div className="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
      <div className="px-4 py-1 bg-gray-100 dark:bg-gray-800 text-xs font-medium text-gray-600 dark:text-gray-400 rounded-full border border-gray-200 dark:border-gray-700">
        {date}
      </div>
      <div className="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
    </div>
  );
};

export default DateSeparator;

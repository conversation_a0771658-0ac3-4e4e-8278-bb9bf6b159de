import React, { useState, useRef, useEffect } from 'react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "Ask me about your trip in Hong Kong..."
}) => {
  const [message, setMessage] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const MAX_LENGTH = 500;
  const remainingChars = MAX_LENGTH - message.length;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !disabled && message.length <= MAX_LENGTH) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= MAX_LENGTH) {
      setMessage(newValue);
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Focus management
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  // Quick suggestions
  const quickSuggestions = [
    "How do I get from Tsim Sha Tsui to Central?",
    "What's the cheapest way to Causeway Bay?",
    "Fastest route to the Airport",
    "MTR service status today"
  ];

  const handleSuggestionClick = (suggestion: string) => {
    setMessage(suggestion);
    textareaRef.current?.focus();
  };

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      {/* Quick Suggestions */}
      {message.length === 0 && !disabled && (
        <div className="p-3 sm:p-4 pb-2 border-b border-gray-100 dark:border-gray-700">
          <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">Quick suggestions:</div>
          <div className="flex flex-wrap gap-1 sm:gap-2">
            {quickSuggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="text-xs px-2 sm:px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 transition-colors touch-manipulation"
                aria-label={`Quick suggestion: ${suggestion}`}
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      <div className="p-3 sm:p-4">
        <form onSubmit={handleSubmit} className="flex items-end space-x-2 sm:space-x-3">
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleChange}
              onKeyPress={handleKeyPress}
              onFocus={handleFocus}
              onBlur={handleBlur}
              placeholder={placeholder}
              disabled={disabled}
              rows={1}
              aria-label="Type your message"
              aria-describedby="char-counter input-help"
              className={`w-full resize-none rounded-2xl border px-3 sm:px-4 py-3 pr-12 sm:pr-16 text-sm transition-all duration-200 touch-manipulation ${
                isFocused
                  ? 'border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20'
                  : 'border-gray-300 dark:border-gray-600'
              } ${
                remainingChars < 50 && remainingChars >= 0
                  ? 'border-orange-400 dark:border-orange-500'
                  : ''
              } ${
                remainingChars < 0
                  ? 'border-red-500 dark:border-red-400'
                  : ''
              } bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed overflow-y-auto`}
              style={{ minHeight: '48px', maxHeight: '120px' }}
            />

            {/* Character counter */}
            <div
              id="char-counter"
              className={`absolute bottom-2 right-10 sm:right-14 text-xs ${
                remainingChars < 50 && remainingChars >= 0
                  ? 'text-orange-500 dark:text-orange-400'
                  : remainingChars < 0
                    ? 'text-red-500 dark:text-red-400'
                    : 'text-gray-400 dark:text-gray-500'
              }`}
              aria-live="polite"
            >
              {remainingChars}
            </div>
          </div>

          <button
            type="submit"
            disabled={!message.trim() || disabled || remainingChars < 0}
            aria-label={disabled ? "Sending message..." : "Send message"}
            className={`flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 touch-manipulation ${
              !message.trim() || disabled || remainingChars < 0
                ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl hover:scale-105'
            }`}
          >
            {disabled ? (
              <svg className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            )}
          </button>
        </form>

        <div
          id="input-help"
          className="mt-2 flex flex-col sm:flex-row sm:justify-between sm:items-center text-xs text-gray-500 dark:text-gray-400 space-y-1 sm:space-y-0"
        >
          <span className="hidden sm:inline">Press Enter to send, Shift+Enter for new line</span>
          <span className="sm:hidden">Tap send button or press Enter</span>
          {message.length > 0 && (
            <span className={remainingChars < 0 ? 'text-red-500 dark:text-red-400' : ''}>
              {message.length}/{MAX_LENGTH}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
